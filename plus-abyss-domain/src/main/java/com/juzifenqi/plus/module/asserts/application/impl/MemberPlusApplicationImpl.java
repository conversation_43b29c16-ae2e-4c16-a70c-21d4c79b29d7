package com.juzifenqi.plus.module.asserts.application.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.enums.CouponTypeEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProfitsGroupEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusCashbackApplication;
import com.juzifenqi.plus.module.asserts.application.ao.MemberPlusSendResultAo;
import com.juzifenqi.plus.module.asserts.model.MemberVirtualModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberCashbackModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberCouponModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberLiftAmountModel;
import com.juzifenqi.plus.module.asserts.model.PlusMemberProductModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.JuziCouponUserEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponEntity;
import com.juzifenqi.plus.module.asserts.model.event.PlusMemberProfitsSendEvent;
import com.juzifenqi.plus.module.asserts.model.event.coupon.ReceiveCouponEvent;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.repository.external.acl.VirtualExternalRepositoryAcl;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberPlusApplicationImpl implements IMemberPlusApplication {

    @Autowired
    private PlusMemberCouponModel          memberCouponModel;
    @Autowired
    private PlusMemberLiftAmountModel      memberLiftAmountModel;
    @Autowired
    private PlusMemberCashbackModel        memberCashbackModel;
    @Autowired
    private IMemberPlusCashbackApplication cashbackApplication;
    @Autowired
    private IPlusProgramQueryModel         plusProgramQueryModel;
    @Autowired
    private VirtualExternalRepositoryAcl   virtualExternalRepositoryAcl;
    @Autowired
    private MemberVirtualModel             memberVirtualModel;
    @Autowired
    private PlusMemberProductModel         plusMemberProductModel;

    /**
     * 权益发放
     */
    @Override
    public MemberPlusSendResultAo sendPlusProfits(PlusMemberProfitsSendEvent sendEvent) {
        log.info("发放权益入参：{}", JSON.toJSONString(sendEvent));
        MemberPlusSendPlanEntity plusMemberProfitsSendPlan = sendEvent.getSendPlanEntity();
        PlusModelEnum profitsType = PlusModelEnum.getPlusModelEnum(
                plusMemberProfitsSendPlan.getModelId());
        PlusProfitsGroupEnum profitsGroup = PlusProfitsGroupEnum.getByValue(
                plusMemberProfitsSendPlan.getProfitType());
        String orderSn = sendEvent.getSendPlanEntity().getOrderSn();
        Integer modelId = sendEvent.getSendPlanEntity().getModelId();
        MemberPlusSendResultAo ao = new MemberPlusSendResultAo();
        switch (profitsGroup) {
            case PLUS_PRODUCT:
                log.info("会员商品权益无显式发放");
                break;
            case PLUS_PRODUCT_VIRTUAL:
                log.info("发放虚拟商品类权益开始：{},{}", modelId, orderSn);
                switch (profitsType) {
                    case LYFF:
                        log.info("发放0元权益开始：{},{}", modelId, orderSn);
                        boolean result = plusMemberProductModel.sendVirtualGoods(
                                plusMemberProfitsSendPlan);
                        ao.setSendVirtualResult(result);
                        log.info("发放0元权益结束：{},{}", modelId, orderSn);
                        break;
                    case LMQY:
                        log.info("发放联名权益开始：{},{}", modelId, orderSn);
                        plusMemberProductModel.sendLmk(plusMemberProfitsSendPlan);
                        ao.setSendVirtualResult(true);
                        log.info("发放联名权益结束：{},{}", modelId, orderSn);
                        break;
                    default:
                        break;
                }
                log.info("发放虚拟商品类权益结束：{},{}", modelId, orderSn);
                break;
            case PLUS_COUPON: {
                log.info("发放券类权益开始：{},{}", modelId, orderSn);
                JuziCouponUserEntity juziCouponUserEntity = memberCouponModel.sendCoupon(
                        plusMemberProfitsSendPlan);
                ao.setJuziCouponUserEntity(juziCouponUserEntity);
                log.info("发放券类权益结束：{},{}", modelId, orderSn);
                break;
            }
            case PLUS_CREDIT: {
                PlusLiftAmountEntity plusLiftAmountEntity = plusProgramQueryModel.getByProgramId(
                        plusMemberProfitsSendPlan.getProgramId());
                memberLiftAmountModel.sendPlusLiftAmount(plusMemberProfitsSendPlan,
                        plusLiftAmountEntity);
                break;
            }
            case PLUS_SPEED:
                log.info("加速权益无显式发放");
                break;
            case PLUS_CASH: {
                // 购物返现、结清返现、还款返现分开处理
                switch (profitsType) {
                    case JQFX: {
                        cashbackApplication.sendJQFXCashback(plusMemberProfitsSendPlan);
                        break;
                    }
                    case GWFX: {
                        memberCashbackModel.sendGWFXCash(plusMemberProfitsSendPlan);
                        break;
                    }
                    case HKFX:
                        cashbackApplication.sendHkfxCashback(plusMemberProfitsSendPlan);
                        break;
                    default:
                        log.error("遇到未知的返现类型");
                }
                break;
            }
            case PLUS_OTHER:
                log.info("暂无其他权益发放");
                break;
            case PLUS_VIRTUAL:
                memberVirtualModel.sendVirtual(plusMemberProfitsSendPlan);
                break;
            default:
                break;
        }
        return ao;
    }

    @Override
    public MemberPlusSendResultAo receiveCoupon(ReceiveCouponEvent event) {
        log.info("原优惠券领取开始：{}", JSON.toJSONString(event));
        MemberPlusSendResultAo ao = new MemberPlusSendResultAo();
        // 获取各优惠券表信息
        MemberCouponEntity coupon = memberCouponModel.getMemberCouponById(event.getSendPlanId(),
                event.getType());
        if (coupon == null) {
            throw new PlusAbyssException("领取失败,未查询到您的优惠券信息记录");
        }
        if (!Objects.equals(coupon.getMemberId(), event.getUserId())) {
            throw new PlusAbyssException("领取失败,优惠券归属非当前登录用户");
        }
        if (Objects.nonNull(coupon.getReachStatus())) {
            if (coupon.getReachStatus() == 0) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300017);
            }
        }
        if (Objects.nonNull(coupon.getReceiveStatus())) {
            if (coupon.getReceiveStatus() == 1) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300011);
            }
            if (coupon.getReceiveStatus() == 2) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300012);
            }
        }
        CouponTypeEnum typeEnum = CouponTypeEnum.getByCode(event.getType());
        // 领取优惠券
        JuziCouponUserEntity juziCouponUserEntity = memberCouponModel.sendCouponOld(coupon,
                typeEnum.getModelId());
        ao.setJuziCouponUserEntity(juziCouponUserEntity);
        return ao;
    }

    /**
     * 权益取消
     */
    @Override
    public void cancelByOrderSn(PlusOrderCancelEvent plusOrderCancelEvent, PlusOrderEntity order,
            List<MemberPlusInfoDetailExtPo> moveList) {
        String orderSn = order.getOrderSn();
        Integer userId = order.getUserId();
        Integer cancelType = plusOrderCancelEvent.getCancelType();
        // 过期会员不需要降额、失效优惠券
        if (cancelType != PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()) {
            // 失效优惠券
            memberCouponModel.cancelCoupon(orderSn, userId, order.getConfigId());
            // 降额
            if (JuziPlusEnum.HYYK_CARD.getCode() != order.getConfigId()
                    || order.getMonthPeriod() == 1) {
                memberLiftAmountModel.cancelPlusLiftAmount(order);
            }
        }
        // 无条件/按比例/未过期/过期取消，通知分销取消待入账的返现
        if (cancelType == PlusCancelTypeEnum.NO_CONDITION.getValue()
                || cancelType == PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                || cancelType == PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue()
                || cancelType == PlusCancelTypeEnum.RATIO.getValue()) {
            // 取消返现（购物返现）
            memberCashbackModel.cancelCash(userId, orderSn);
        }
        // 取消虚拟权益订单(酒店券)
        virtualExternalRepositoryAcl.cancelVirtualOrder(orderSn);
    }
}
