package com.juzifenqi.plus.module.program.application.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.dto.req.detail.LandReq;
import com.juzifenqi.plus.dto.req.detail.VirtualQueryReq;
import com.juzifenqi.plus.dto.resp.PlusBasicInfoReq;
import com.juzifenqi.plus.enums.SupplierEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.common.IContractExternalRepository;
import com.juzifenqi.plus.module.common.IDecryptRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.order.application.IPlusOrderShuntApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusShuntResultEntity;
import com.juzifenqi.plus.module.order.model.event.order.ShuntEvent;
import com.juzifenqi.plus.module.program.application.IPlusLandDetailApplication;
import com.juzifenqi.plus.module.program.model.IPlusLandDetailModel;
import com.juzifenqi.plus.module.program.model.IPlusProfitQueryModel;
import com.juzifenqi.plus.module.program.model.converter.IPlusProgramConverter;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.juzishuke.contract.dto.rpc.response.ContractTemplatePreviewRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 落地页/权益页
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:26
 */
@Service
@Slf4j
public class PlusLandDetailApplicationImpl implements IPlusLandDetailApplication {

    private final IPlusProgramConverter converter = IPlusProgramConverter.instance;

    @Autowired
    private IPlusLandDetailModel       detailModel;
    @Autowired
    private IPlusProfitQueryModel      profitQueryModel;
    @Autowired
    private MemberProfitsQueryModel    memberProfitsQueryModel;
    @Autowired
    private IPlusOrderShuntApplication shuntApplication;
    @Autowired
    private MemberPlusQueryModel       memberPlusQueryModel;
    @Autowired
    private IDecryptRepository         decryptRepository;
    @Autowired
    private IMemberExternalRepository  memberExternalRepository;
    @Autowired
    private IPlusShuntRepository       shuntRepository;
    @Autowired
    private ConfigProperties           configProperties;
    @Autowired
    private IContractExternalRepository contractExternalRepository;

    @Override
    public LandDetailEntity getLandDetail(LandReq event) {
        return detailModel.getLandDetail(event);
    }

    @Override
    public LandOldDetailEntity getOldLandDetail(LandReq event) {
        LandOldDetailEntity oldLandDetail = detailModel.getOldLandDetail(event);
        // 分流
        if (event.isNeedShunt() && oldLandDetail.getCanBuy() == 1) {
            BigDecimal programPrice =
                    oldLandDetail.getDiscountPrice() == null ? oldLandDetail.getMallMobilePrice()
                            : oldLandDetail.getDiscountPrice();
            ShuntEvent shuntEvent = converter.toShuntEvent(event.getUserId(), event.getChannelId(),
                    programPrice, event.getConfigId());
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(shuntEvent);
            oldLandDetail.setShuntSupplierId(shuntResult.getSupplierId());
            oldLandDetail.setContractList(shuntResult.getContractList());
            oldLandDetail.setSupplierName(shuntResult.getSupplierName());
        }
        // 不需要分流(从营销入口跳转过来的，已经分流过了)需要返回对应分流主体对应名称和合同列表
        if (!event.isNeedShunt()) {
            // 优先以传入的分流主体id为准，默认桔子
            Integer supplierId = event.getSupplierId() == null ? configProperties.defaultSupplierId
                    : event.getSupplierId();
            PlusShuntSupplierEntity supplierCache = shuntRepository.getSupplierCache(supplierId);
            if (supplierCache != null) {
                oldLandDetail.setContractList(supplierCache.getContractList());
                oldLandDetail.setSupplierName(supplierCache.getSupplierName());
            }
        }
        // 月卡合同
        addMonthlyCardContracts(oldLandDetail.getContractList(), event.getChannelId(), event.getUserId());
        return oldLandDetail;
    }

    @Override
    public List<String> getPlusBuyRecords(Integer programId) {
        return detailModel.getPlusBuyRecords(programId);
    }

    @Override
    public List<LandVirtualProductTypeEntity> getVirtualProductList(VirtualQueryReq req) {
        if (StringUtils.isNotBlank(req.getOrderSn())) {
            HandleProfitQueryEvent event = converter.toHandleProfitQueryEvent(req);
            List<ProfitVirtualProductTypeEntity> list = memberProfitsQueryModel.getProfitVirtualProductTypeEntityList(
                    event);
            return converter.toLandVirtualProductTypeEntityList(list);
        }
        return profitQueryModel.getVirtualProductList(req.getProgramId());
    }

    @Override
    public LandOldDetailEntity getLandCommonDetail(LandReq event) {
        return detailModel.getLandCommonDetail(event);
    }

    @Override
    public Map<String, Object> getPlusBasicInfo(PlusBasicInfoReq req) {
        log.info("详情页获取用户信息，入参:{}", JSON.toJSONString(req));
        MemberPlusInfoDetailEntity memberPlusInfoEntity = memberPlusQueryModel.getCurrentMemberDetail(
                req.getUserId(), req.getConfigId());
        if (memberPlusInfoEntity == null) {
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300001);
        }
        MemberInfo memberInfo = memberExternalRepository.getById(req.getUserId());
        if (memberInfo == null) {
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300002);
        }
        String expireDateStr = "";
        String memberName = "桔享PLUS";
        int plusProgramId = 0;
        Map<String, Object> data = new HashMap<>(8);
        if (memberPlusInfoEntity.getJxStatus() != null && memberPlusInfoEntity.getJxStatus() == 1) {
            Date jxEndTime = memberPlusInfoEntity.getJxEndTime();
            if (jxEndTime != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                expireDateStr = sdf.format(jxEndTime) + " 到期";
            }
        }
        data.put("expireDateStr", expireDateStr);
        String userName = memberInfo.getRealName();
        if (userName == null) {
            // 20220115 zhangjianfeng  更改为取密文解密
            String mobile = decryptRepository.decrypt(memberInfo.getMobileDes(),
                    "会员详情页获取顶部数据,解密手机号");
            userName = mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        } else {
            String lastChar = org.apache.commons.lang3.StringUtils.right(userName, 1);
            userName = org.apache.commons.lang3.StringUtils.leftPad(lastChar, userName.length(),
                    "*");
        }
        data.put("userName", userName);
        data.put("userImgUrl", memberInfo.getHeadPortrait());
        Integer programId = memberPlusInfoEntity.getProgramId();
        if (Objects.nonNull(programId)) {
            memberName = memberPlusInfoEntity.getConfigName();
            plusProgramId = programId;
        }
        data.put("memberName", memberName);
        data.put("plusProgramId", plusProgramId);
        return data;
    }

    /**
     * 添加月卡合同信息到合同列表
     *
     * @param contractList 合同列表
     * @param channelCode  渠道编码
     * @param customerId   客户ID
     */
    private void addMonthlyCardContracts(List<PlusShuntSupplierContractEntity> contractList,
                                         Integer channelCode, Integer customerId) {
        List<ContractTemplatePreviewRes> contractTemplatePreviewResList =
                contractExternalRepository.queryContractTemplatePreviewList(channelCode.toString(), customerId);
        if (CollectionUtils.isEmpty(contractTemplatePreviewResList)) {
            return;
        }
        contractTemplatePreviewResList.forEach(template -> {
            PlusShuntSupplierContractEntity contractEntity = new PlusShuntSupplierContractEntity();
            contractEntity.setContractName(template.getContractName());
            contractEntity.setContractNo(template.getContractTemplateCode());
            contractEntity.setSupplierId(null);
            contractList.add(contractEntity);
        });
    }
}
