package com.juzifenqi.plus.module.order.model.impl;

import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.ChannelFunctionEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.event.PlusSmsParamEvent;
import com.juzifenqi.plus.module.order.model.PlusOrderMsgNoticeModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IMessageExternalRepository;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderMsgNoticeModelConverter;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageMqOutEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageOutEvent;
import com.juzifenqi.plus.module.program.model.IPlusChannelManagerQueryModel;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单消息通知
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:48
 */
@Component
public class PlusOrderMsgNoticeModelImpl implements PlusOrderMsgNoticeModel {

    private final IPlusOrderMsgNoticeModelConverter converter = IPlusOrderMsgNoticeModelConverter.instance;

    @Autowired
    private IMessageExternalRepository messageExternalRepository;
    @Autowired
    private ISmsRepository                smsRepository;
    @Autowired
    private IPlusChannelManagerQueryModel channelManagerModel;

    @Override
    public void sendCancelNotice(PlusOrderCancelEvent event, PlusOrderEntity plusOrder) {
        if (JuziPlusEnum.HYYK_CARD.getCode() != plusOrder.getConfigId()
                || plusOrder.getMonthPeriod() == 1) {
            // 发送消息到消息中心
            PlusOrderMessageOutEvent plusOrderMessageOutEvent = converter.toOrderMessageOutEvent(event,
                    "MO22");
            messageExternalRepository.sendMessageCenter(plusOrderMessageOutEvent);
            // 发送取消成功mq
            PlusOrderMessageMqOutEvent mqOutEvent = converter.toPlusOrderMessageMqOutEvent(event,
                    CommonConstant.TWO);
            messageExternalRepository.sendPlusMq(mqOutEvent);
        }
    }

    /**
     * 非商城渠道短信通知
     */
    @Override
    public void sendChannelCancelMessage(PlusOrderCancelEvent event, PlusOrderEntity plusOrder) {
//        if (Objects.equals(plusOrder.getChannelId(), ChannelEnum.A.getCode())) {
//            // 商城渠道不处理
//            return;
//        }
//        // 发送渠道短信（有条件、无条件、按比例、延迟、极速）
//        boolean sendSms = channelManagerModel.supportSms(plusOrder.getChannelId(),
//                plusOrder.getConfigId(), ChannelFunctionEnum.NOTE_REACH_SUCCESS.getCode());
//        if (sendSms) {
//            //发送短信
//            PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
//            plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NODE_1.getCode());
//            plusSmsParamVo.setProgramId(plusOrder.getProgramId());
//            plusSmsParamVo.setUserId(plusOrder.getUserId());
//            plusSmsParamVo.setConfigId(plusOrder.getConfigId());
//            plusSmsParamVo.setChannelId(plusOrder.getChannelId());
//            plusSmsParamVo.setOrderSn(plusOrder.getOrderSn());
//            plusSmsParamVo.setName(plusOrder.getProgramName());
//            smsRepository.sendSmsByConfig(plusSmsParamVo);
//        }
//        Integer cancelType = event.getCancelType();
    }
}
