package com.juzifenqi.plus.module.common.repository.external.acl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.module.common.IContractExternalRepository;
import com.juzishuke.contract.api.ContractRpcProvider;
import com.juzishuke.contract.dto.rpc.request.ContractTemplatePreviewListReq;
import com.juzishuke.contract.dto.rpc.request.InternalDownloadContractReq;
import com.juzishuke.contract.dto.rpc.response.ContractTemplatePreviewRes;
import com.juzishuke.contract.dto.rpc.response.DownloadContractRes;
import com.juzishuke.framework.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 合同系统
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17 17:35
 */
@Service
@Slf4j
public class ContractExternalRepositoryAcl implements IContractExternalRepository {

    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private ContractRpcProvider provider;

    @Override
    public List<DownloadContractRes> getContract(Integer userId, String orderId) {
        InternalDownloadContractReq req = new InternalDownloadContractReq();
        req.setCustomerId(userId);
        req.setOrderId(orderId);
        log.info("调用合同系统获取合同列表入参：{}", JSON.toJSONString(req));
        BaseResponse<List<DownloadContractRes>> response = provider.internalDownloadContract(req);
        log.info("调用合同系统获取合同列表返回：{}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            return null;
        }
        return response.getData();
    }

    @Override
    public List<ContractTemplatePreviewRes> queryContractTemplatePreviewList(String channelCode, Integer customerId) {
        ContractTemplatePreviewListReq req = new ContractTemplatePreviewListReq();
        req.setChannelCode(channelCode);
        req.setNodeCode(configProperties.contractNodeCode);
        req.setCustomerId(customerId);
        log.info("获取合同模板预览列表接口入参:{}", JSON.toJSONString(req));
        BaseResponse<List<ContractTemplatePreviewRes>> response = provider.queryContractTemplatePreviewList(req);
        log.info("获取合同模板预览列表接口入参返回：{}", JSON.toJSONString(response));
        if (!response.isSuccess()) {
            return null;
        }
        return response.getData();
    }
}
