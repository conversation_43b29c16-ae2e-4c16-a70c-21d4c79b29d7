package com.juzifenqi.plus.enums;

/**
 * 黑名单类型枚举
 *
 * <AUTHOR>
 * @date 2022 08 10
 */
public enum BlackListTypeEnum {

    /**
     *
     */
    BLACK_TYPE_1(1, "后付款黑名单"), BLACK_TYPE_2(2, "会员黑名单"), BLACK_TYPE_3(3, "中原黑名单"),
    BLACK_TYPE_4(4, "会员月卡黑名单");

    private Integer code;

    private String name;


    BlackListTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
