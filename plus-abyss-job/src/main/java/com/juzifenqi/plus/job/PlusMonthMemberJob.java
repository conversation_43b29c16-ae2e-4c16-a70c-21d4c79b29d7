package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusMonthMemberApi;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员月卡续费创单/划扣任务
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Slf4j
@Component
public class PlusMonthMemberJob {
    @Autowired
    private IPlusMonthMemberApi plusMonthMemberApi;

    @Autowired
    private IPlusOrderApi plusOrderApi;


    /**
     * 会员月卡：根据续费计划创建续费订单
     */
    @XxlJob("plusMonthMemberCreateOrder")
    public ReturnT<String> plusMonthMemberCreateOrder() {
        try {
            log.info("----会员月卡根据计划创单--job开始----");
            plusMonthMemberApi.createRenewalOrdersBySchedule();
            log.info("---会员月卡根据计划创单--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("会员月卡根据计划创单 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 会员月卡定时扣费任务
     */
    @XxlJob("plusMonthMemberDeduct")
    public ReturnT<String> plusMonthMemberDeduct() {
        try {
            log.info("----会员月卡定时扣费--job开始----");
            plusOrderApi.monthCardDeductJob();
            log.info("----会员月卡定时扣费--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("会员月卡定时扣费异常 ", e);
        }
        return ReturnT.FAIL;
    }
}
